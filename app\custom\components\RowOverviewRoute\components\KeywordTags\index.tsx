import React from 'react';

const KeywordTags = ({tags,type}:{tags:any,type?:string}) => {
  const filledTags = ['Keyword 1', 'Keyword 2', 'Keyword 3'];
  const outlinedTags = [
    'gather',
    'Manager',
    'UIUX',
    'Financial Services',
    'design websites',
    'Manager',
    'Graphic Designer'
  ];
  return (
    <div className="max-w-full text-xs text-blue-950 max-md:ml-2.5">
      <div className="">
        {type&&type=="single"?(tags.map((tag:any, index:any) => (
          <span
            key={`filled-${index}`}
            className="self-stretch !text-foreground !font-medium !text-[14px] max-w-[100px]"
          >
            {tag}
          </span>)
        )):(<div className='flex flex-wrap gap-2 col-span-full'>{tags.map((tag:any, index:any) => (
          <div
            key={`filled-${index}`}
            className="self-stretch !text-foreground !font-medium !text-[14px] max-w-[100px] border-[1px] border-input py-1 px-2 rounded-[6px]"
          >
            {tag}
          </div>)
        )}
        </div>)
        }
        {/* {outlinedTags.map((tag, index) => (
          <span
            key={`outlined-${index}`}
            className="gap-1 self-stretch px-2 py-px my-auto leading-loose whitespace-nowrap rounded border border-gray-200 border-solid bg-black bg-opacity-0"
          >
            {tag}
          </span>
        ))} */}
        {/* <span className="self-stretch my-auto font-medium text-center text-slate-700">
          +12 more
        </span>
        <span className="gap-1 self-stretch px-2 py-px my-auto leading-loose rounded border border-gray-200 border-dashed">
          New Tag
        </span> */}
      </div>
    </div>
  );
};

export default KeywordTags;